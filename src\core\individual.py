"""
个体数据结构实现

定义Individual类，表示模拟中的单个个体，包含人口统计学信息、
健康状态和历史跟踪功能。
"""

import uuid
from datetime import datetime, date
from typing import List, Optional, Dict, Any
from dataclasses import dataclass, field

from .enums import DiseaseState, PathwayType, CancerStage, Gender


@dataclass
class HealthEvent:
    """健康事件记录"""
    timestamp: datetime
    event_type: str
    from_state: Optional[DiseaseState]
    to_state: DiseaseState
    age_at_event: float
    additional_data: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """验证健康事件数据"""
        if self.age_at_event < 0:
            raise ValueError("年龄不能为负数")
        if not isinstance(self.to_state, DiseaseState):
            raise ValueError("目标状态必须是DiseaseState枚举")


class Individual:
    """
    个体类
    
    表示模拟中的单个个体，包含基本人口统计学信息、
    当前健康状态和完整的健康历史记录。
    """

    def __init__(
        self,
        birth_year: int,
        gender: Gender,
        individual_id: Optional[str] = None,
        initial_disease_state: DiseaseState = DiseaseState.NORMAL,
        pathway_type: Optional[PathwayType] = None,
    ):
        """
        初始化个体

        Args:
            birth_year: 出生年份
            gender: 性别
            individual_id: 个体唯一标识符，如果为None则自动生成
            initial_disease_state: 初始疾病状态
            pathway_type: 疾病进展通路类型
        """
        self.individual_id = individual_id or str(uuid.uuid4())
        self.birth_year = birth_year
        self.gender = gender
        self.current_disease_state = initial_disease_state
        self.pathway_type = pathway_type
        self.cancer_stage: Optional[CancerStage] = None
        self.health_history: List[HealthEvent] = []

        # 风险因素配置文件（延迟导入以避免循环依赖）
        self._risk_factor_profile: Optional[Any] = None

        # 验证初始数据
        self._validate_initial_data()

        # 记录初始状态
        self._record_initial_state()

    def _record_initial_state(self) -> None:
        """记录初始健康状态"""
        initial_event = HealthEvent(
            timestamp=datetime.now(),
            event_type="initialization",
            from_state=None,
            to_state=self.current_disease_state,
            age_at_event=self.get_current_age(),
            additional_data={"pathway_type": self.pathway_type.value if self.pathway_type else None}
        )
        self.health_history.append(initial_event)

    def _validate_initial_data(self) -> None:
        """验证初始数据的有效性"""
        current_year = datetime.now().year
        
        if not (1900 <= self.birth_year <= current_year):
            raise ValueError(f"出生年份必须在1900-{current_year}之间")
        
        if not isinstance(self.gender, Gender):
            raise ValueError("性别必须是Gender枚举")
        
        if not isinstance(self.current_disease_state, DiseaseState):
            raise ValueError("疾病状态必须是DiseaseState枚举")
        
        # 验证通路类型与疾病状态的兼容性
        if self.pathway_type and self.current_disease_state != DiseaseState.NORMAL:
            compatible_states = self.pathway_type.get_compatible_states()
            if self.current_disease_state not in compatible_states:
                raise ValueError(f"疾病状态{self.current_disease_state}与通路类型{self.pathway_type}不兼容")

    def get_current_age(self, reference_year: Optional[int] = None) -> float:
        """
        获取当前年龄
        
        Args:
            reference_year: 参考年份，如果为None则使用当前年份
            
        Returns:
            当前年龄（浮点数）
        """
        if reference_year is None:
            reference_year = datetime.now().year
        return float(reference_year - self.birth_year)

    def transition_to_state(
        self,
        new_state: DiseaseState,
        cancer_stage: Optional[CancerStage] = None,
        additional_data: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        转换到新的疾病状态
        
        Args:
            new_state: 新的疾病状态
            cancer_stage: 癌症分期（仅在癌症状态下需要）
            additional_data: 额外的事件数据
            
        Returns:
            转换是否成功
        """
        # 验证状态转换的有效性
        if not self._is_valid_transition(self.current_disease_state, new_state):
            return False
        
        # 验证癌症分期
        if new_state.is_cancer() and cancer_stage is None:
            raise ValueError("癌症状态必须指定癌症分期")
        
        if not new_state.is_cancer() and cancer_stage is not None:
            raise ValueError("非癌症状态不能指定癌症分期")
        
        # 记录状态转换事件
        event = HealthEvent(
            timestamp=datetime.now(),
            event_type="state_transition",
            from_state=self.current_disease_state,
            to_state=new_state,
            age_at_event=self.get_current_age(),
            additional_data=additional_data or {}
        )
        
        # 更新状态
        self.current_disease_state = new_state
        self.cancer_stage = cancer_stage
        self.health_history.append(event)
        
        return True

    def _is_valid_transition(self, from_state: DiseaseState, to_state: DiseaseState) -> bool:
        """
        验证疾病状态转换是否有效
        
        Args:
            from_state: 当前状态
            to_state: 目标状态
            
        Returns:
            转换是否有效
        """
        # 死亡状态不能转换到其他状态
        if from_state.is_death():
            return False
        
        # 定义有效的状态转换规则
        valid_transitions = {
            DiseaseState.NORMAL: {
                DiseaseState.LOW_RISK_ADENOMA,
                DiseaseState.SMALL_SERRATED,
                DiseaseState.DEATH_OTHER,
            },
            DiseaseState.LOW_RISK_ADENOMA: {
                DiseaseState.HIGH_RISK_ADENOMA,
                DiseaseState.NORMAL,  # 通过治疗
                DiseaseState.DEATH_OTHER,
            },
            DiseaseState.HIGH_RISK_ADENOMA: {
                DiseaseState.PRECLINICAL_CANCER,
                DiseaseState.NORMAL,  # 通过治疗
                DiseaseState.DEATH_OTHER,
            },
            DiseaseState.SMALL_SERRATED: {
                DiseaseState.LARGE_SERRATED,
                DiseaseState.NORMAL,  # 通过治疗
                DiseaseState.DEATH_OTHER,
            },
            DiseaseState.LARGE_SERRATED: {
                DiseaseState.PRECLINICAL_CANCER,
                DiseaseState.NORMAL,  # 通过治疗
                DiseaseState.DEATH_OTHER,
            },
            DiseaseState.PRECLINICAL_CANCER: {
                DiseaseState.CLINICAL_CANCER,
                DiseaseState.DEATH_OTHER,
            },
            DiseaseState.CLINICAL_CANCER: {
                DiseaseState.DEATH_CANCER,
                DiseaseState.DEATH_OTHER,
                DiseaseState.NORMAL,  # 通过治疗治愈
            },
        }
        
        return to_state in valid_transitions.get(from_state, set())

    def get_health_history_by_type(self, event_type: str) -> List[HealthEvent]:
        """
        按事件类型获取健康历史
        
        Args:
            event_type: 事件类型
            
        Returns:
            指定类型的健康事件列表
        """
        return [event for event in self.health_history if event.event_type == event_type]

    def get_time_in_state(self, state: DiseaseState) -> float:
        """
        计算在特定状态下的时间
        
        Args:
            state: 疾病状态
            
        Returns:
            在该状态下的总时间（年）
        """
        total_time = 0.0
        current_state_start = None
        
        for event in self.health_history:
            if event.to_state == state:
                current_state_start = event.age_at_event
            elif current_state_start is not None and event.from_state == state:
                total_time += event.age_at_event - current_state_start
                current_state_start = None
        
        # 如果当前仍在该状态
        if current_state_start is not None and self.current_disease_state == state:
            total_time += self.get_current_age() - current_state_start
        
        return total_time

    def is_alive(self) -> bool:
        """检查个体是否存活"""
        return not self.current_disease_state.is_death()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        result = {
            "individual_id": self.individual_id,
            "birth_year": self.birth_year,
            "gender": self.gender.value,
            "current_disease_state": self.current_disease_state.value,
            "pathway_type": self.pathway_type.value if self.pathway_type else None,
            "cancer_stage": self.cancer_stage.value if self.cancer_stage else None,
            "current_age": self.get_current_age(),
            "is_alive": self.is_alive(),
            "health_history_count": len(self.health_history),
        }

        # 添加风险因素信息
        if self._risk_factor_profile:
            result["risk_factors"] = self._risk_factor_profile.to_dict()

        return result

    def __repr__(self) -> str:
        """字符串表示"""
        return (
            f"Individual(id={self.individual_id[:8]}..., "
            f"age={self.get_current_age():.1f}, "
            f"gender={self.gender.value}, "
            f"state={self.current_disease_state.value})"
        )

    # 风险因素管理方法

    @property
    def risk_factor_profile(self):
        """获取风险因素配置文件"""
        if self._risk_factor_profile is None:
            # 延迟导入以避免循环依赖
            from ..modules.disease.risk_factors import RiskFactorProfile
            self._risk_factor_profile = RiskFactorProfile(individual_id=self.individual_id)
        return self._risk_factor_profile

    def set_risk_factor_profile(self, profile) -> None:
        """设置风险因素配置文件"""
        self._risk_factor_profile = profile

    def add_risk_factor(self, risk_factor) -> None:
        """添加风险因素"""
        self.risk_factor_profile.add_factor(risk_factor)

    def remove_risk_factor(self, factor_type) -> None:
        """移除风险因素"""
        self.risk_factor_profile.remove_factor(factor_type)

    def get_risk_factor(self, factor_type):
        """获取指定类型的风险因素"""
        return self.risk_factor_profile.get_factor(factor_type)

    def has_risk_factor(self, factor_type) -> bool:
        """检查是否存在指定类型的风险因素"""
        return self.risk_factor_profile.has_factor(factor_type)

    def get_risk_factor_value(self, factor_type, default=None):
        """获取风险因素值"""
        return self.risk_factor_profile.get_factor_value(factor_type, default)

    def update_risk_factor_value(self, factor_type, value, source: str = "update") -> None:
        """更新风险因素值"""
        self.risk_factor_profile.update_factor_value(factor_type, value, source)

    def get_risk_factors_by_category(self, category: str) -> Dict:
        """按类别获取风险因素"""
        return self.risk_factor_profile.get_factors_by_category(category)
