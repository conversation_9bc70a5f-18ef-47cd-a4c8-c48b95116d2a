"""
风险因素管理模块

定义和管理个体风险因素，包括：
- 风险因素类型枚举
- 风险因素数据结构
- 风险因素配置文件管理
"""

from enum import Enum
from dataclasses import dataclass, field
from datetime import datetime
from typing import Union, Dict, List, Optional, Any, Set
from pathlib import Path
import json
import yaml


class RiskFactorType(Enum):
    """风险因素类型枚举"""
    
    # 遗传因素
    FAMILY_HISTORY = "family_history"  # 家族史（布尔）
    
    # 疾病相关因素
    IBD = "inflammatory_bowel_disease"  # 炎症性肠病（布尔）
    DIABETES = "diabetes_mellitus"  # 糖尿病（布尔）
    
    # 生活方式因素
    BMI = "body_mass_index"  # 体重指数（连续）
    SMOKING = "smoking_status"  # 吸烟状态（布尔/分类）
    ALCOHOL_CONSUMPTION = "alcohol_consumption"  # 酒精消费（连续）
    SEDENTARY_LIFESTYLE = "sedentary_lifestyle"  # 久坐生活方式（连续/小时）
    DIET_QUALITY = "diet_quality"  # 饮食质量（评分）
    PHYSICAL_ACTIVITY = "physical_activity"  # 体力活动（连续/小时）
    
    @classmethod
    def get_genetic_factors(cls) -> Set["RiskFactorType"]:
        """获取遗传相关风险因素"""
        return {cls.FAMILY_HISTORY}
    
    @classmethod
    def get_disease_factors(cls) -> Set["RiskFactorType"]:
        """获取疾病相关风险因素"""
        return {cls.IBD, cls.DIABETES}
    
    @classmethod
    def get_lifestyle_factors(cls) -> Set["RiskFactorType"]:
        """获取生活方式相关风险因素"""
        return {
            cls.BMI, cls.SMOKING, cls.ALCOHOL_CONSUMPTION,
            cls.SEDENTARY_LIFESTYLE, cls.DIET_QUALITY, cls.PHYSICAL_ACTIVITY
        }
    
    @classmethod
    def get_boolean_factors(cls) -> Set["RiskFactorType"]:
        """获取布尔型风险因素"""
        return {cls.FAMILY_HISTORY, cls.IBD, cls.DIABETES, cls.SMOKING}
    
    @classmethod
    def get_continuous_factors(cls) -> Set["RiskFactorType"]:
        """获取连续值风险因素"""
        return {
            cls.BMI, cls.ALCOHOL_CONSUMPTION, cls.SEDENTARY_LIFESTYLE,
            cls.DIET_QUALITY, cls.PHYSICAL_ACTIVITY
        }
    
    def is_genetic(self) -> bool:
        """判断是否为遗传因素"""
        return self in self.get_genetic_factors()
    
    def is_disease_related(self) -> bool:
        """判断是否为疾病相关因素"""
        return self in self.get_disease_factors()
    
    def is_lifestyle(self) -> bool:
        """判断是否为生活方式因素"""
        return self in self.get_lifestyle_factors()
    
    def is_boolean(self) -> bool:
        """判断是否为布尔型因素"""
        return self in self.get_boolean_factors()
    
    def is_continuous(self) -> bool:
        """判断是否为连续值因素"""
        return self in self.get_continuous_factors()


class RiskLevel(Enum):
    """风险等级枚举"""
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    VERY_HIGH = "very_high"


@dataclass
class RiskFactor:
    """
    风险因素数据类
    
    表示个体的单个风险因素，包含类型、值、权重等信息。
    """
    factor_type: RiskFactorType
    value: Union[bool, float, int, str]
    weight: float = 1.0
    last_updated: datetime = field(default_factory=datetime.now)
    source: str = "default"
    confidence: float = 1.0  # 数据可信度 (0-1)
    
    def __post_init__(self):
        """初始化后验证"""
        self._validate_value()
    
    def _validate_value(self) -> None:
        """验证风险因素值的有效性"""
        if self.factor_type.is_boolean():
            if not isinstance(self.value, bool):
                raise ValueError(f"布尔型风险因素 {self.factor_type.value} 的值必须是布尔类型")
        
        elif self.factor_type.is_continuous():
            if not isinstance(self.value, (int, float)):
                raise ValueError(f"连续型风险因素 {self.factor_type.value} 的值必须是数值类型")
            
            # 特定因素的范围验证
            if self.factor_type == RiskFactorType.BMI:
                if not (10.0 <= self.value <= 60.0):
                    raise ValueError(f"BMI值必须在10-60之间，当前值: {self.value}")
            
            elif self.factor_type == RiskFactorType.SEDENTARY_LIFESTYLE:
                if not (0.0 <= self.value <= 24.0):
                    raise ValueError(f"久坐时间必须在0-24小时之间，当前值: {self.value}")
            
            elif self.factor_type == RiskFactorType.DIET_QUALITY:
                if not (0.0 <= self.value <= 100.0):
                    raise ValueError(f"饮食质量评分必须在0-100之间，当前值: {self.value}")
    
    def update_value(self, new_value: Union[bool, float, int, str], source: str = "update") -> None:
        """更新风险因素值"""
        old_value = self.value
        self.value = new_value
        self.source = source
        self.last_updated = datetime.now()
        
        try:
            self._validate_value()
        except ValueError:
            # 如果新值无效，恢复旧值
            self.value = old_value
            raise
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "factor_type": self.factor_type.value,
            "value": self.value,
            "weight": self.weight,
            "last_updated": self.last_updated.isoformat(),
            "source": self.source,
            "confidence": self.confidence
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "RiskFactor":
        """从字典创建风险因素"""
        return cls(
            factor_type=RiskFactorType(data["factor_type"]),
            value=data["value"],
            weight=data.get("weight", 1.0),
            last_updated=datetime.fromisoformat(data["last_updated"]),
            source=data.get("source", "default"),
            confidence=data.get("confidence", 1.0)
        )


@dataclass
class RiskFactorProfile:
    """
    风险因素配置文件
    
    管理个体的所有风险因素集合。
    """
    individual_id: str
    factors: Dict[RiskFactorType, RiskFactor] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def add_factor(self, risk_factor: RiskFactor) -> None:
        """添加风险因素"""
        self.factors[risk_factor.factor_type] = risk_factor
        self.updated_at = datetime.now()
    
    def remove_factor(self, factor_type: RiskFactorType) -> None:
        """移除风险因素"""
        if factor_type in self.factors:
            del self.factors[factor_type]
            self.updated_at = datetime.now()
    
    def get_factor(self, factor_type: RiskFactorType) -> Optional[RiskFactor]:
        """获取指定类型的风险因素"""
        return self.factors.get(factor_type)
    
    def has_factor(self, factor_type: RiskFactorType) -> bool:
        """检查是否存在指定类型的风险因素"""
        return factor_type in self.factors
    
    def get_factor_value(self, factor_type: RiskFactorType, default: Any = None) -> Any:
        """获取风险因素值"""
        factor = self.get_factor(factor_type)
        return factor.value if factor else default
    
    def update_factor_value(self, factor_type: RiskFactorType, value: Any, source: str = "update") -> None:
        """更新风险因素值"""
        if factor_type in self.factors:
            self.factors[factor_type].update_value(value, source)
            self.updated_at = datetime.now()
        else:
            # 如果因素不存在，创建新的
            self.add_factor(RiskFactor(factor_type, value, source=source))
    
    def get_factors_by_category(self, category: str) -> Dict[RiskFactorType, RiskFactor]:
        """按类别获取风险因素"""
        if category == "genetic":
            target_types = RiskFactorType.get_genetic_factors()
        elif category == "disease":
            target_types = RiskFactorType.get_disease_factors()
        elif category == "lifestyle":
            target_types = RiskFactorType.get_lifestyle_factors()
        else:
            raise ValueError(f"未知的风险因素类别: {category}")
        
        return {
            factor_type: factor
            for factor_type, factor in self.factors.items()
            if factor_type in target_types
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "individual_id": self.individual_id,
            "factors": {
                factor_type.value: factor.to_dict()
                for factor_type, factor in self.factors.items()
            },
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "RiskFactorProfile":
        """从字典创建风险因素配置文件"""
        profile = cls(
            individual_id=data["individual_id"],
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"])
        )
        
        for factor_type_str, factor_data in data["factors"].items():
            factor_type = RiskFactorType(factor_type_str)
            factor = RiskFactor.from_dict(factor_data)
            profile.factors[factor_type] = factor
        
        return profile
