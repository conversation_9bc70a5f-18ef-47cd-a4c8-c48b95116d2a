"""
风险因素权重配置管理模块

负责加载、验证和管理风险因素权重配置。
"""

import yaml
import json
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime

from .risk_factors import RiskFactorType, RiskLevel


@dataclass
class WeightMetadata:
    """权重配置元数据"""
    version: str
    description: str
    source: str
    last_updated: str
    references: list = field(default_factory=list)


@dataclass
class RiskFactorWeight:
    """单个风险因素权重配置"""
    weight: float
    confidence: float
    description: str
    evidence_level: str
    references: list = field(default_factory=list)
    
    # 特殊权重配置（用于连续变量）
    base_weight: Optional[float] = None
    per_unit_increase: Optional[float] = None
    per_hour_increase: Optional[float] = None
    per_point_decrease: Optional[float] = None
    threshold: Optional[float] = None
    max_weight: Optional[float] = None
    min_weight: Optional[float] = None
    
    # BMI特殊配置
    normal_range: Optional[list] = None
    weights: Optional[Dict[str, float]] = None


class RiskFactorWeights:
    """
    风险因素权重管理器
    
    负责加载、验证和提供风险因素权重配置。
    """
    
    def __init__(self, config_path: Optional[Union[str, Path]] = None):
        """
        初始化权重管理器
        
        Args:
            config_path: 配置文件路径，默认使用内置配置
        """
        self.config_path = config_path
        self.metadata: Optional[WeightMetadata] = None
        self.weights: Dict[RiskFactorType, RiskFactorWeight] = {}
        self.age_adjustment: Dict[str, float] = {}
        self.gender_adjustment: Dict[str, float] = {}
        self.risk_stratification: Dict[str, float] = {}
        
        # 加载配置
        self._load_config()
    
    def _load_config(self) -> None:
        """加载权重配置"""
        if self.config_path is None:
            # 使用默认配置文件
            default_path = Path(__file__).parent.parent.parent.parent / "data" / "risk_factor_weights" / "default_weights.yaml"
            self.config_path = default_path
        
        config_path = Path(self.config_path)
        if not config_path.exists():
            raise FileNotFoundError(f"权重配置文件不存在: {config_path}")
        
        # 根据文件扩展名选择加载方式
        if config_path.suffix.lower() in ['.yaml', '.yml']:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
        elif config_path.suffix.lower() == '.json':
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
        else:
            raise ValueError(f"不支持的配置文件格式: {config_path.suffix}")
        
        self._parse_config(config_data)
    
    def _parse_config(self, config_data: Dict[str, Any]) -> None:
        """解析配置数据"""
        # 解析元数据
        if 'metadata' in config_data:
            meta = config_data['metadata']
            self.metadata = WeightMetadata(
                version=meta.get('version', '1.0'),
                description=meta.get('description', ''),
                source=meta.get('source', ''),
                last_updated=meta.get('last_updated', ''),
                references=meta.get('references', [])
            )
        
        # 解析风险因素权重
        if 'risk_factor_weights' in config_data:
            for factor_name, weight_config in config_data['risk_factor_weights'].items():
                try:
                    factor_type = RiskFactorType(factor_name)
                    weight = RiskFactorWeight(
                        weight=weight_config.get('weight', 1.0),
                        confidence=weight_config.get('confidence', 1.0),
                        description=weight_config.get('description', ''),
                        evidence_level=weight_config.get('evidence_level', ''),
                        references=weight_config.get('references', []),
                        base_weight=weight_config.get('base_weight'),
                        per_unit_increase=weight_config.get('per_unit_increase'),
                        per_hour_increase=weight_config.get('per_hour_increase'),
                        per_point_decrease=weight_config.get('per_point_decrease'),
                        threshold=weight_config.get('threshold'),
                        max_weight=weight_config.get('max_weight'),
                        min_weight=weight_config.get('min_weight'),
                        normal_range=weight_config.get('normal_range'),
                        weights=weight_config.get('weights')
                    )
                    self.weights[factor_type] = weight
                except ValueError:
                    # 跳过未知的风险因素类型
                    continue
        
        # 解析年龄调整参数
        self.age_adjustment = config_data.get('age_adjustment', {})
        
        # 解析性别调整参数
        self.gender_adjustment = config_data.get('gender_adjustment', {})
        
        # 解析风险分层阈值
        self.risk_stratification = config_data.get('risk_stratification', {})
    
    def get_weight(self, factor_type: RiskFactorType) -> float:
        """获取风险因素的基础权重"""
        if factor_type not in self.weights:
            return 1.0  # 默认权重
        return self.weights[factor_type].weight
    
    def get_weight_config(self, factor_type: RiskFactorType) -> Optional[RiskFactorWeight]:
        """获取风险因素的完整权重配置"""
        return self.weights.get(factor_type)
    
    def calculate_factor_weight(self, factor_type: RiskFactorType, value: Any, age: Optional[int] = None) -> float:
        """
        计算特定值的风险因素权重
        
        Args:
            factor_type: 风险因素类型
            value: 风险因素值
            age: 年龄（用于年龄调整）
            
        Returns:
            计算后的权重值
        """
        weight_config = self.get_weight_config(factor_type)
        if not weight_config:
            return 1.0
        
        # 布尔型风险因素
        if factor_type.is_boolean():
            return weight_config.weight if value else 1.0
        
        # 连续型风险因素的特殊处理
        if factor_type == RiskFactorType.BMI:
            return self._calculate_bmi_weight(value, weight_config)
        
        elif factor_type == RiskFactorType.ALCOHOL_CONSUMPTION:
            return self._calculate_alcohol_weight(value, weight_config)
        
        elif factor_type == RiskFactorType.SEDENTARY_LIFESTYLE:
            return self._calculate_sedentary_weight(value, weight_config)
        
        elif factor_type == RiskFactorType.DIET_QUALITY:
            return self._calculate_diet_weight(value, weight_config)
        
        elif factor_type == RiskFactorType.PHYSICAL_ACTIVITY:
            return self._calculate_activity_weight(value, weight_config)
        
        # 默认处理
        return weight_config.weight
    
    def _calculate_bmi_weight(self, bmi: float, config: RiskFactorWeight) -> float:
        """计算BMI权重"""
        if not config.weights or not config.normal_range:
            return config.weight
        
        if bmi < 18.5:
            return config.weights.get('underweight', 1.0)
        elif bmi < 25.0:
            return config.weights.get('normal', 1.0)
        elif bmi < 30.0:
            return config.weights.get('overweight', 1.0)
        elif bmi < 35.0:
            return config.weights.get('obese_1', 1.0)
        elif bmi < 40.0:
            return config.weights.get('obese_2', 1.0)
        else:
            return config.weights.get('obese_3', 1.0)
    
    def _calculate_alcohol_weight(self, alcohol_grams: float, config: RiskFactorWeight) -> float:
        """计算酒精消费权重"""
        if config.threshold and alcohol_grams <= config.threshold:
            return config.base_weight or 1.0
        
        base = config.base_weight or 1.0
        increase = config.per_unit_increase or 0.0
        threshold = config.threshold or 0.0
        
        excess = max(0, alcohol_grams - threshold)
        weight = base + (excess / 10.0) * increase  # 每10g为一个单位
        
        if config.max_weight:
            weight = min(weight, config.max_weight)
        
        return weight
    
    def _calculate_sedentary_weight(self, hours: float, config: RiskFactorWeight) -> float:
        """计算久坐时间权重"""
        if config.threshold and hours <= config.threshold:
            return config.base_weight or 1.0
        
        base = config.base_weight or 1.0
        increase = config.per_hour_increase or 0.0
        threshold = config.threshold or 0.0
        
        excess = max(0, hours - threshold)
        weight = base + excess * increase
        
        if config.max_weight:
            weight = min(weight, config.max_weight)
        
        return weight
    
    def _calculate_diet_weight(self, score: float, config: RiskFactorWeight) -> float:
        """计算饮食质量权重"""
        base = config.base_weight or 1.0
        decrease = config.per_point_decrease or 0.0
        optimal = config.optimal_score or 100.0
        
        weight = base - (score / optimal) * decrease * optimal
        
        if config.min_weight:
            weight = max(weight, config.min_weight)
        
        return weight
    
    def _calculate_activity_weight(self, hours_per_week: float, config: RiskFactorWeight) -> float:
        """计算体力活动权重"""
        if config.threshold and hours_per_week <= config.threshold:
            return config.base_weight or 1.0
        
        base = config.base_weight or 1.0
        decrease = config.per_hour_decrease or 0.0
        threshold = config.threshold or 0.0
        
        excess = max(0, hours_per_week - threshold)
        weight = base - excess * decrease
        
        if config.min_weight:
            weight = max(weight, config.min_weight)
        
        return weight
    
    def get_age_adjustment_factor(self, age: int) -> float:
        """获取年龄调整因子"""
        base_age = self.age_adjustment.get('base_age', 50)
        per_year = self.age_adjustment.get('per_year_increase', 0.05)
        max_effect = self.age_adjustment.get('max_age_effect', 3.0)
        
        age_diff = age - base_age
        factor = 1.0 + age_diff * per_year
        
        return min(factor, max_effect)
    
    def get_gender_adjustment_factor(self, gender: str) -> float:
        """获取性别调整因子"""
        return self.gender_adjustment.get(gender.lower(), 1.0)
    
    def classify_risk_level(self, risk_score: float) -> RiskLevel:
        """根据风险评分分类风险等级"""
        if risk_score >= self.risk_stratification.get('very_high_risk', 4.0):
            return RiskLevel.VERY_HIGH
        elif risk_score >= self.risk_stratification.get('high_risk', 2.5):
            return RiskLevel.HIGH
        elif risk_score >= self.risk_stratification.get('moderate_risk', 1.5):
            return RiskLevel.MODERATE
        else:
            return RiskLevel.LOW
