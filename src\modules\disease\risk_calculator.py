"""
风险评分计算引擎

实现综合风险评分计算，包括：
- 加权风险评分算法
- 年龄和性别调整
- 风险分层功能
- 风险评分历史跟踪
"""

import math
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field

from .risk_factors import RiskFactorType, RiskFactorProfile, RiskLevel
from .risk_weights import RiskFactorWeights


@dataclass
class RiskScore:
    """风险评分结果"""
    individual_id: str
    total_score: float
    risk_level: RiskLevel
    component_scores: Dict[RiskFactorType, float] = field(default_factory=dict)
    age_adjustment: float = 1.0
    gender_adjustment: float = 1.0
    calculation_date: datetime = field(default_factory=datetime.now)
    algorithm_version: str = "1.0"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "individual_id": self.individual_id,
            "total_score": self.total_score,
            "risk_level": self.risk_level.value,
            "component_scores": {
                factor_type.value: score
                for factor_type, score in self.component_scores.items()
            },
            "age_adjustment": self.age_adjustment,
            "gender_adjustment": self.gender_adjustment,
            "calculation_date": self.calculation_date.isoformat(),
            "algorithm_version": self.algorithm_version
        }


@dataclass
class RiskTrend:
    """风险趋势分析"""
    individual_id: str
    scores: List[RiskScore] = field(default_factory=list)
    trend_direction: str = "stable"  # "increasing", "decreasing", "stable"
    trend_magnitude: float = 0.0
    
    def add_score(self, score: RiskScore) -> None:
        """添加新的风险评分"""
        self.scores.append(score)
        self.scores.sort(key=lambda x: x.calculation_date)
        self._calculate_trend()
    
    def _calculate_trend(self) -> None:
        """计算风险趋势"""
        if len(self.scores) < 2:
            self.trend_direction = "stable"
            self.trend_magnitude = 0.0
            return
        
        # 计算最近两次评分的变化
        recent_score = self.scores[-1].total_score
        previous_score = self.scores[-2].total_score
        
        change = recent_score - previous_score
        relative_change = abs(change) / previous_score if previous_score > 0 else 0
        
        self.trend_magnitude = relative_change
        
        if relative_change < 0.05:  # 5%以内认为稳定
            self.trend_direction = "stable"
        elif change > 0:
            self.trend_direction = "increasing"
        else:
            self.trend_direction = "decreasing"


class RiskCalculator:
    """
    风险评分计算器
    
    负责计算个体的综合风险评分，支持多种算法和调整因子。
    """
    
    def __init__(self, weights: Optional[RiskFactorWeights] = None):
        """
        初始化风险计算器
        
        Args:
            weights: 风险因素权重配置，如果为None则使用默认配置
        """
        self.weights = weights or RiskFactorWeights()
        self.risk_histories: Dict[str, RiskTrend] = {}
    
    def calculate_risk_score(
        self,
        individual_id: str,
        risk_profile: RiskFactorProfile,
        age: int,
        gender: str,
        algorithm: str = "multiplicative"
    ) -> RiskScore:
        """
        计算个体综合风险评分
        
        Args:
            individual_id: 个体ID
            risk_profile: 风险因素配置文件
            age: 年龄
            gender: 性别
            algorithm: 计算算法 ("multiplicative", "additive", "log_linear")
            
        Returns:
            风险评分结果
        """
        # 计算各风险因素的权重
        component_scores = {}
        
        for factor_type, risk_factor in risk_profile.factors.items():
            factor_weight = self.weights.calculate_factor_weight(
                factor_type, risk_factor.value, age
            )
            component_scores[factor_type] = factor_weight
        
        # 根据算法计算总评分
        if algorithm == "multiplicative":
            total_score = self._calculate_multiplicative_score(component_scores)
        elif algorithm == "additive":
            total_score = self._calculate_additive_score(component_scores)
        elif algorithm == "log_linear":
            total_score = self._calculate_log_linear_score(component_scores)
        else:
            raise ValueError(f"不支持的算法: {algorithm}")
        
        # 应用年龄和性别调整
        age_adjustment = self.weights.get_age_adjustment_factor(age)
        gender_adjustment = self.weights.get_gender_adjustment_factor(gender)
        
        adjusted_score = total_score * age_adjustment * gender_adjustment
        
        # 确定风险等级
        risk_level = self.weights.classify_risk_level(adjusted_score)
        
        # 创建风险评分对象
        risk_score = RiskScore(
            individual_id=individual_id,
            total_score=adjusted_score,
            risk_level=risk_level,
            component_scores=component_scores,
            age_adjustment=age_adjustment,
            gender_adjustment=gender_adjustment
        )
        
        # 更新风险历史
        self._update_risk_history(risk_score)
        
        return risk_score
    
    def _calculate_multiplicative_score(self, component_scores: Dict[RiskFactorType, float]) -> float:
        """乘法模型：各风险因素权重相乘"""
        score = 1.0
        for weight in component_scores.values():
            score *= weight
        return score
    
    def _calculate_additive_score(self, component_scores: Dict[RiskFactorType, float]) -> float:
        """加法模型：各风险因素权重相加"""
        base_score = 1.0
        additional_risk = sum(weight - 1.0 for weight in component_scores.values())
        return base_score + additional_risk
    
    def _calculate_log_linear_score(self, component_scores: Dict[RiskFactorType, float]) -> float:
        """对数线性模型：对数尺度上的线性组合"""
        log_score = 0.0
        for weight in component_scores.values():
            if weight > 0:
                log_score += math.log(weight)
        return math.exp(log_score)
    
    def _update_risk_history(self, risk_score: RiskScore) -> None:
        """更新风险评分历史"""
        individual_id = risk_score.individual_id
        
        if individual_id not in self.risk_histories:
            self.risk_histories[individual_id] = RiskTrend(individual_id=individual_id)
        
        self.risk_histories[individual_id].add_score(risk_score)
    
    def get_risk_trend(self, individual_id: str) -> Optional[RiskTrend]:
        """获取个体风险趋势"""
        return self.risk_histories.get(individual_id)
    
    def compare_risk_profiles(
        self,
        profile1: RiskFactorProfile,
        profile2: RiskFactorProfile,
        age: int,
        gender: str
    ) -> Dict[str, Any]:
        """
        比较两个风险配置文件
        
        Args:
            profile1: 第一个风险配置文件
            profile2: 第二个风险配置文件
            age: 年龄
            gender: 性别
            
        Returns:
            比较结果
        """
        score1 = self.calculate_risk_score("temp1", profile1, age, gender)
        score2 = self.calculate_risk_score("temp2", profile2, age, gender)
        
        return {
            "profile1_score": score1.total_score,
            "profile2_score": score2.total_score,
            "score_difference": score2.total_score - score1.total_score,
            "relative_change": (score2.total_score - score1.total_score) / score1.total_score,
            "risk_level_change": {
                "from": score1.risk_level.value,
                "to": score2.risk_level.value
            }
        }
    
    def identify_high_impact_factors(
        self,
        risk_profile: RiskFactorProfile,
        age: int,
        gender: str
    ) -> List[Tuple[RiskFactorType, float]]:
        """
        识别高影响风险因素
        
        Args:
            risk_profile: 风险因素配置文件
            age: 年龄
            gender: 性别
            
        Returns:
            按影响程度排序的风险因素列表
        """
        base_score = self.calculate_risk_score("temp", risk_profile, age, gender)
        
        factor_impacts = []
        
        for factor_type in risk_profile.factors:
            # 创建移除该因素的配置文件
            modified_profile = RiskFactorProfile(individual_id="temp_modified")
            for ft, rf in risk_profile.factors.items():
                if ft != factor_type:
                    modified_profile.add_factor(rf)
            
            # 计算移除该因素后的评分
            modified_score = self.calculate_risk_score("temp_modified", modified_profile, age, gender)
            
            # 计算影响程度
            impact = base_score.total_score - modified_score.total_score
            factor_impacts.append((factor_type, impact))
        
        # 按影响程度排序
        factor_impacts.sort(key=lambda x: abs(x[1]), reverse=True)
        
        return factor_impacts
    
    def simulate_risk_reduction(
        self,
        risk_profile: RiskFactorProfile,
        age: int,
        gender: str,
        interventions: Dict[RiskFactorType, Any]
    ) -> Dict[str, Any]:
        """
        模拟风险干预效果
        
        Args:
            risk_profile: 当前风险配置文件
            age: 年龄
            gender: 性别
            interventions: 干预措施 {风险因素类型: 目标值}
            
        Returns:
            干预效果分析
        """
        # 计算当前风险评分
        current_score = self.calculate_risk_score("current", risk_profile, age, gender)
        
        # 创建干预后的配置文件
        intervention_profile = RiskFactorProfile(individual_id="intervention")
        
        for factor_type, risk_factor in risk_profile.factors.items():
            if factor_type in interventions:
                # 应用干预
                new_value = interventions[factor_type]
                modified_factor = risk_factor
                modified_factor.value = new_value
                intervention_profile.add_factor(modified_factor)
            else:
                intervention_profile.add_factor(risk_factor)
        
        # 计算干预后的风险评分
        intervention_score = self.calculate_risk_score("intervention", intervention_profile, age, gender)
        
        return {
            "current_risk": {
                "score": current_score.total_score,
                "level": current_score.risk_level.value
            },
            "intervention_risk": {
                "score": intervention_score.total_score,
                "level": intervention_score.risk_level.value
            },
            "risk_reduction": {
                "absolute": current_score.total_score - intervention_score.total_score,
                "relative": (current_score.total_score - intervention_score.total_score) / current_score.total_score,
                "percentage": ((current_score.total_score - intervention_score.total_score) / current_score.total_score) * 100
            },
            "interventions_applied": interventions
        }
    
    def get_risk_summary(self, individual_id: str) -> Optional[Dict[str, Any]]:
        """获取个体风险摘要"""
        trend = self.get_risk_trend(individual_id)
        if not trend or not trend.scores:
            return None
        
        latest_score = trend.scores[-1]
        
        return {
            "individual_id": individual_id,
            "current_risk": {
                "score": latest_score.total_score,
                "level": latest_score.risk_level.value
            },
            "trend": {
                "direction": trend.trend_direction,
                "magnitude": trend.trend_magnitude
            },
            "history_count": len(trend.scores),
            "last_updated": latest_score.calculation_date.isoformat()
        }
